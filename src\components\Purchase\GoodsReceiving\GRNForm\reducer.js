/* eslint-disable unicorn/prefer-structured-clone */
import dayjs from 'dayjs';
import { notification } from 'antd';
import Helpers from '@Apis/helpers';
import { QUANTITY } from '@Apis/constants';

function applyDiscount(copyData, discountType, grnTypeValue, value, billFromAddress, billToAddress) {
  if (grnTypeValue !== 'ADHOC') {
    copyData.map((item) => {
      const totalValue = copyData.reduce((acc, cur) => acc + (cur.received_qty * cur.offer_price), 0);

      const discountValue = discountType === 'Percent'
        ? Number.parseFloat(value)
        : ((item.received_qty * item.offer_price) / Number.parseFloat(totalValue)) * Number.parseFloat(value);

      const taxableValue = discountType === 'Percent'
        ? (item.received_qty * item.offer_price) * (1 - discountValue / 100)
        : Math.max(item.received_qty * item.offer_price - discountValue, 0);

      item.discount = discountValue;
      item.child_taxes = Helpers.computeTaxation(
        taxableValue,
        item?.taxInfo,
        billFromAddress,
        billToAddress
      )?.tax_info?.child_taxes;

      return item;
    });
  } else {
    copyData.map((item) => {
      const totalValue = copyData.reduce((acc, cur) => acc + (cur.quantity * cur.offer_price), 0);

      const discountValue = discountType === 'Percent'
        ? Number.parseFloat(value)
        : ((item.quantity * item.offer_price) / Number.parseFloat(totalValue)) * Number.parseFloat(value);

      const taxableValue = discountType === 'Percent'
        ? (item.quantity * item.offer_price) * (1 - discountValue / 100)
        : Math.max(item.quantity * item.offer_price - discountValue, 0);

      item.discount = discountValue;
      item.child_taxes = Helpers.computeTaxation(
        taxableValue,
        item?.taxInfo,
        billFromAddress,
        billToAddress
      )?.tax_info?.child_taxes;

      return item;
    });
  }

  return { data: copyData, discountPercentage: Number.parseFloat(value) };
}

export const initialState = {
  // Dates
  grnDate: dayjs(),
  invoiceDate: dayjs(),
  dueDate: dayjs(),

  // GRN / PO
  grnTypeValue: 'Purchase Order',
  data: [],
  chargeData: [],
  toggleRejectedBatches: false,
  showModal: false,
  getHistory: '',
  latestGrnId: '',
  selectedPoValue: [],
  discountPercentage: 0,
  selectedPo: null,
  vendorAddress: null,
  formSubmitted: false,

  // Tax & Currency
  taxTypeName: 'TDS',
  selectedCurrencyInfo: '',
  selectedCurrencyID: '',
  charge1Name: 'Freight',
  charge1Value: '',
  discountType: 'Percent',
  freightTaxId: 'Not Applicable',
  freightTax: null,
  openFreightTax: false,
  isAutomaticConversionRate: null,
  currencyConversionRate: '',
  freightTaxData: {
    child_taxes: [
      {
        tax_amount: 0,
        tax_type_name: '',
      },
    ],
  },

  // Stock / Validation
  allStock: true,
  isLineWiseDiscount: false,
  isMultipleBatchModeEnabled: false,
  multipleBatchValidation: false,
  isBatchValid: false,
  visibleLineCfs: [],
  batchNumbers: new Map([]),

  // Tenant & Integration
  tenantDepartmentId: '',
  selectedTenant: null,
  selectedTenantTallyIntegrationId: null, // will need to be set via INIT action

  // Misc
  updateDocumentReason: '',
  narration: '',
  paymentTerms: 0,
  paymentRemarks: '',
};

export const ACTIONS = {
  UPDATE_FIELD: 'UPDATE_FIELD', // generic update for single field
  UPDATE_FIELDS: 'UPDATE_FIELDS', // bulk update (multiple fields at once)
  UPDATE_DATA: 'UPDATE_DATA', // update data
  RESET_FORM: 'RESET_FORM', // reset to initialState
  INIT_STATE: 'INIT_STATE', // initialize with external data (props, API)
  DELETE_ITEM: 'DELETE_GRN_ITEM', // delete grn item and recalculate discount if needed
  DELETE_CHARGE: 'DELETE_CHARGE', // delete charge and recalculate discount if needed
  HANDLE_FULL_QUANTITY: 'HANDLE_FULL_QUANTITY',
  RECORD_FULL_QUANTITY: 'RECORD_FULL_QUANTITY',
  REMOVE_ZERO_QUANTITY: 'REMOVE_ZERO_QUANTITY',
  UPDATE_TABLE_VALUE: 'UPDATE_TABLE_VALUE',
};

export const formReducer = (state, action) => {
  switch (action.type) {
  case ACTIONS.UPDATE_FIELD: {
    return {
      ...state,
      [action.field]: action.value,
    };
  }

  case ACTIONS.UPDATE_FIELDS: {
    return {
      ...state,
      ...action.payload, // payload = { field1: value1, field2: value2 }
    };
  }

  case ACTIONS.UPDATE_DATA: {
    return {
      ...state,
      data: action.payload,
    };
  }

  case ACTIONS.INIT_STATE: {
    return {
      ...state,
      ...action.payload,
    };
  }

  case ACTIONS.DELETE_ITEM: {
    const { key, id, billFromAddress, billToAddress } = action.payload;
    const { data, selectedPoValue, isLineWiseDiscount, discountPercentage, discountType, grnTypeValue } = state;

    if (data.length <= 1) {
      notification.error({
        message: 'You need to have at least one item in the GRN.',
        duration: 4,
        placement: 'top',
      });
      return state;
    }

    const copyData = data.filter(
      (item) => item.key !== key || item.grn_line_id !== id
    );

    const updatedSelectedPoValue = selectedPoValue?.filter((item) =>
      copyData?.some((data) => data?.po_id === item?.value)
    );

    let newState = {
      ...state,
      data: copyData,
      selectedPoValue: updatedSelectedPoValue,
    };

    if (isLineWiseDiscount) {
      const { data: discountedData, discountPercentage: dp } = applyDiscount(
        JSON.parse(JSON.stringify(copyData)), // pass the *new* data
        discountType,
        grnTypeValue,
        discountPercentage,
        billFromAddress,
        billToAddress
      );

      newState = {
        ...newState,
        data: discountedData,
        discountPercentage: dp,
      };
    }

    return newState;
  }

  case ACTIONS.DELETE_CHARGE: {
    const { chargeKey } = action.payload;
    return {
      ...state,
      chargeData: state?.chargeData.filter(
        (item) => item.chargeKey !== chargeKey
      ),
    };
  }

  case ACTIONS.HANDLE_FULL_QUANTITY: {
    const { item, billFromAddress, billToAddress } = action.payload;

    // guard: don’t update if negative
    if (Number(item?.quantity) - Number(item?.total_received_qty) < 0) {
      return state;
    }

    const updatedData = state?.data.map((obj) => {
      if (!item?.grn_line_id && (obj?.po_line_id === item?.po_line_id) && item?.po_line_id) {
        const discountValue = Number(item?.discount) || 0;
        const totalPrice = ((Number(item?.quantity) - Number(item?.total_received_qty)) * item.offer_price);

        const taxableValue = item?.lineDiscountType === 'Percent'
          ? totalPrice * (1 - discountValue / 100)
          : Math.max(totalPrice - discountValue, 0);

        return {
          ...obj,
          received_qty: Number(item?.quantity) - Number(item?.total_received_qty),
          child_taxes: Helpers.computeTaxation(
            taxableValue,
            item?.taxInfo,
            billFromAddress,
            billToAddress
          )?.tax_info?.child_taxes,
        };
      }
      return obj;
    });

    return {
      ...state,
      data: updatedData,
    };
  }

  case ACTIONS.RECORD_FULL_QUANTITY: {
    const { billFromAddress, billToAddress } = action.payload;

    const updatedData = state.data.map((item) => {
      if (Number(item?.quantity) - Number(item?.total_received_qty) >= 0 && item.po_line_id) {
        const discountValue = Number(item?.discount) || 0;
        const totalPrice = (Number(item?.quantity) - Number(item?.total_received_qty)) * item.offer_price;

        const taxableValue = item?.lineDiscountType === 'Percent'
          ? totalPrice * (1 - discountValue / 100)
          : Math.max(totalPrice - discountValue, 0);

        return {
          ...item,
          received_qty: QUANTITY(
            Number(item?.ordered_qty || item?.quantity) - Number(item?.total_received_qty || 0),
            item?.uom_info?.[0]?.precision
          ),
          child_taxes: Helpers.computeTaxation(
            taxableValue,
            item?.taxInfo,
            billFromAddress,
            billToAddress
          )?.tax_info?.child_taxes,
        };
      }
      return item;
    });

    return {
      ...state,
      data: updatedData,
    };
  }

  case ACTIONS.REMOVE_ZERO_QUANTITY: {
    const updatedData = state?.data.filter(
      (item) => !(item?.received_qty === 0 || item?.received_qty === '')
    );

    return {
      ...state,
      data: updatedData,
    };
  }

  case ACTIONS.UPDATE_TABLE_VALUE: {
    const { key, value, label } = action.payload;

    const updatedData = state?.data?.map((obj) => {
      if (obj?.po_line_id === key && obj?.po_line_id) {
        return {
          ...obj,
          [label]: value,
        };
      }
      return obj;
    });

    return {
      ...state,
      data: updatedData,
    };
  }

  case ACTIONS.RESET_FORM: {
    return { ...initialState };
  }

  default: {
    return state;
  }
  }
};
