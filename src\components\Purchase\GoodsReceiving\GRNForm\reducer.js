import dayjs from 'dayjs';

export const initialState = {
  // Dates
  grnDate: dayjs(),
  invoiceDate: dayjs(),
  dueDate: dayjs(),

  // GRN / PO
  grnTypeValue: 'Purchase Order',
  data: [],
  chargeData: [],
  toggleRejectedBatches: false,
  showModal: false,
  getHistory: '',
  latestGrnId: '',

  // Tax & Currency
  taxTypeName: 'TDS',
  selectedCurrencyName: '',
  selectedCurrencyID: '',
  charge1Name: 'Freight',
  charge1Value: '',
  discountType: 'Percent',
  freightTaxId: 'Not Applicable',
  freightTax: null,
  openFreightTax: false,
  isAutomaticConversionRate: null,
  currencyConversionRate: '',
  freightTaxData: {
    child_taxes: [
      {
        tax_amount: 0,
        tax_type_name: '',
      },
    ],
  },

  // Stock / Validation
  allStock: true,
  isLineWiseDiscount: false,
  isMultipleBatchModeEnabled: false,
  multipleBatchValidation: false,
  isBatchValid: false,
  visibleLineCfs: [],
  batchNumbers: new Map([]),

  // Tenant & Integration
  tenantDepartmentId: '',
  selectedTenant: null,
  selectedTenantTallyIntegrationId: null, // will need to be set via INIT action

  // Misc
  updateDocumentReason: '',
  narration: '',
  paymentTerms: 0,
  paymentRemarks: '',
};

export const ACTIONS = {
  UPDATE_FIELD: 'UPDATE_FIELD',     // generic update for single field
  UPDATE_FIELDS: 'UPDATE_FIELDS',   // bulk update (multiple fields at once)
  UPDATE_DATA: 'UPDATE_DATA', // update grnTableData
  RESET_FORM: 'RESET_FORM',         // reset to initialState
  INIT_STATE: 'INIT_STATE',         // initialize with external data (props, API)
};

export const formReducer = (state, action) => {
  switch (action.type) {
  case ACTIONS.UPDATE_FIELD: {
    return {
      ...state,
      [action.field]: action.value,
    };
  }

  case ACTIONS.UPDATE_FIELDS: {
    return {
      ...state,
      ...action.payload, // payload = { field1: value1, field2: value2 }
    };
  }

  case ACTIONS.UPDATE_DATA: {
    return {
      ...state,
      data: action.payload,
    };
  }

  case ACTIONS.INIT_STATE: {
    return {
      ...state,
      ...action.payload, // e.g. { selectedTenantTallyIntegrationId: props?.user?.tenant_info?.it_id }
    };
  }

  case ACTIONS.RESET_FORM: {
    return { ...initialState };
  }

  default: {
    return state;
  }
  }
};
